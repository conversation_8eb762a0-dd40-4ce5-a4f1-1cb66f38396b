
# Critical Password Policy Audit Questions for Enterprise Environments

## Question 1: Password Complexity and Strength Requirements

**Question to Ask:** "What are the organization's current password complexity requirements, and how are these requirements technically enforced across all systems and applications?"

**Why This Question is Critical:**
Weak password requirements are the foundation of most credential-based attacks. Without adequate complexity standards, users create predictable passwords that are easily compromised through brute force, dictionary attacks, or credential stuffing. This represents the first line of defense against unauthorized access.

**Evidence/Documentation to Request:**
- Written password policy documentation with specific complexity requirements
- System configuration screenshots showing password complexity settings in Active Directory, applications, and databases
- Password strength testing results or vulnerability assessments
- Exception documentation for systems that cannot meet standard requirements
- User training materials that communicate password requirements

**Common Weaknesses/Red Flags:**
- Minimum length less than 12 characters for standard users, 15+ for privileged accounts
- No requirements for character diversity (uppercase, lowercase, numbers, symbols)
- Policies that haven't been updated in 2+ years
- Inconsistent enforcement across different systems
- Exceptions granted without proper risk assessment or compensating controls
- Reliance on password hints or security questions as primary authentication

**Potential Business Impact if Control Fails:**
- Unauthorized access to sensitive data leading to data breaches and regulatory fines
- Compromise of privileged accounts enabling lateral movement and system-wide breaches
- Business disruption from ransomware or system compromise
- Loss of customer trust and competitive advantage
- Legal liability and compliance violations (SOX, HIPAA, PCI-DSS, GDPR)

---

## Question 2: Multi-Factor Authentication Implementation

**Question to Ask:** "For which user types, systems, and access scenarios is multi-factor authentication (MFA) required, and what evidence exists that MFA is consistently enforced?"

**Why This Question is Critical:**
Even strong passwords can be compromised through phishing, malware, or social engineering. MFA provides a critical second layer of defense that dramatically reduces the risk of unauthorized access even when passwords are compromised. The absence of MFA on critical systems represents an unacceptable risk in today's threat landscape.

**Evidence/Documentation to Request:**
- MFA policy documentation specifying coverage requirements
- System configuration evidence showing MFA enforcement
- MFA enrollment reports showing coverage percentages by user type
- Exception reports for users or systems without MFA
- Incident reports related to authentication bypass attempts
- MFA technology architecture documentation and backup procedures

**Common Weaknesses/Red Flags:**
- MFA not required for privileged or administrative accounts
- Inconsistent MFA enforcement across cloud and on-premises systems
- Reliance on SMS-based MFA (vulnerable to SIM swapping)
- No MFA for remote access or cloud applications
- Excessive use of "trusted device" exceptions
- Lack of backup authentication methods when primary MFA fails
- No monitoring of MFA bypass attempts or failures

**Potential Business Impact if Control Fails:**
- Account takeover leading to data theft or financial fraud
- Privileged account compromise enabling complete system control
- Regulatory non-compliance in industries requiring strong authentication
- Increased success rate of phishing and social engineering attacks
- Business email compromise (BEC) leading to financial losses

---

## Question 3: Password Storage and Encryption Standards

**Question to Ask:** "How are passwords stored, hashed, and protected across all systems, and what evidence exists that industry-standard cryptographic controls are implemented?"

**Why This Question is Critical:**
Improperly stored passwords can be easily compromised if systems are breached, turning a limited security incident into a catastrophic credential compromise. Weak hashing algorithms or poor implementation can allow attackers to quickly crack passwords and gain widespread access.

**Evidence/Documentation to Request:**
- Technical documentation of password storage mechanisms
- Database and application configuration showing hashing algorithms used
- Salt generation and storage procedures
- Encryption key management documentation
- Code review results for custom applications
- Penetration testing results focusing on password storage
- Legacy system inventory with password storage assessment

**Common Weaknesses/Red Flags:**
- Use of weak hashing algorithms (MD5, SHA-1, unsalted SHA-2)
- Passwords stored in plaintext or reversibly encrypted
- Inadequate salt generation or reuse of salts
- Custom password storage implementations without security review
- Legacy systems with outdated cryptographic standards
- Shared service accounts with hardcoded passwords
- Insufficient protection of password hashes in backups or logs

**Potential Business Impact if Control Fails:**
- Mass credential compromise following any system breach
- Lateral movement across systems using compromised credentials
- Long-term persistent access by attackers using cracked passwords
- Regulatory violations related to data protection standards
- Competitive intelligence theft through compromised accounts

---

## Question 4: Password Lifecycle Management and Rotation

**Question to Ask:** "What processes govern password creation, rotation, and retirement, and how does the organization ensure passwords are changed when security events occur or personnel leave?"

**Why This Question is Critical:**
Poor password lifecycle management creates windows of vulnerability where compromised or outdated credentials remain active. Without proper rotation and retirement processes, former employees, compromised accounts, or leaked passwords can provide persistent unauthorized access.

**Evidence/Documentation to Request:**
- Password rotation policy with specific timeframes
- Automated password rotation logs and reports
- Emergency password change procedures and evidence of execution
- Account deprovisioning procedures and audit trails
- Shared/service account password management documentation
- Incident response procedures for credential compromise
- Password manager deployment and usage statistics

**Common Weaknesses/Red Flags:**
- No mandatory password rotation for privileged accounts
- Excessive password rotation requirements causing user fatigue
- Lack of emergency password change procedures
- Delayed account deprovisioning when employees leave
- Shared accounts with passwords that are never changed
- No process for rotating passwords after security incidents
- Service accounts with static passwords not managed through automated tools

**Potential Business Impact if Control Fails:**
- Persistent unauthorized access by former employees or contractors
- Continued compromise after security incidents due to unchanged passwords
- Service disruptions from expired or forgotten service account passwords
- Insider threat risks from shared account password exposure
- Compliance violations related to access management requirements

---

## Question 5: Password Policy Monitoring and Enforcement

**Question to Ask:** "What monitoring, reporting, and enforcement mechanisms exist to ensure password policy compliance, and how does the organization detect and respond to policy violations?"

**Why This Question is Critical:**
Password policies are only effective if they are consistently monitored and enforced. Without proper oversight, policy violations go undetected, creating security gaps that attackers can exploit. Effective monitoring also provides early warning of potential security incidents.

**Evidence/Documentation to Request:**
- Password policy compliance monitoring reports
- Automated policy enforcement tool configurations
- User training records and policy acknowledgment documentation
- Violation detection and response procedures
- Audit logs showing policy enforcement actions
- Metrics on password policy compliance rates
- Integration between password tools and security monitoring systems

**Common Weaknesses/Red Flags:**
- No automated monitoring of password policy compliance
- Lack of real-time enforcement mechanisms
- Insufficient logging of authentication events and policy violations
- No regular reporting on password security metrics
- Weak consequences for repeated policy violations
- Inadequate user training on password security requirements
- No integration between password management and security incident response

**Potential Business Impact if Control Fails:**
- Gradual degradation of password security posture without detection
- Inability to identify compromised accounts or suspicious authentication patterns
- Regulatory compliance failures due to inadequate monitoring
- Delayed incident response due to lack of authentication visibility
- User behavior that undermines technical password controls
- Increased success rate of credential-based attacks due to poor enforcement

---

## Implementation Notes for Auditors

**Risk Assessment Priority:** Address these questions in order, as each builds upon the previous control. Question 1 (complexity) and Question 2 (MFA) should be considered critical findings if deficient.

**Documentation Standards:** Ensure all evidence is current (within 12 months), includes both policy and technical implementation proof, and covers all systems in scope.

**Follow-up Testing:** Consider technical testing to validate policy implementation, including password cracking attempts on test accounts and MFA bypass testing where appropriate.
